#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
🔧 图片重命名工具 - 简约优化版
提供简洁高效的图片重命名、ASIN映射功能
"""

import os
import pandas as pd
import shutil
import zipfile
from datetime import datetime
from io import BytesIO
from typing import Dict, List, Tuple, Optional
import re
from pathlib import Path

class ImageRenamer:
    """📸 图片重命名处理器 - 简约版"""

    # 支持的图片格式
    SUPPORTED_FORMATS = {'.png', '.jpg', '.jpeg', '.gif', '.bmp', '.webp'}

    # 图片类型映射
    IMAGE_TYPES = {
        '白底图': 'MAIN',
        '场景图': 'PT',
        '色块': 'SWCH'
    }

    def __init__(self, excel_path: str, image_path: str):
        """
        🚀 初始化图片重命名处理器

        Args:
            excel_path: Excel文件路径（包含SKU、ASIN映射）
            image_path: 图片目录路径
        """
        self.excel_path = Path(excel_path)
        self.image_path = Path(image_path)
        self.sku_to_asin = {}
        self.color_to_asin = {}
        self.processed_files = []
        self.error_message = ""
        
    def load_excel_data(self):
        """
        加载Excel数据
        
        Returns:
            bool: 是否成功加载数据
        """
        try:
            df = pd.read_excel(self.excel_path)
            required_columns = ['SKU', 'ASIN']
            missing_columns = [col for col in required_columns if col not in df.columns]
            
            if missing_columns:
                self.error_message = f'Excel文件缺少必要的列：{", ".join(missing_columns)}'
                raise Exception(self.error_message)
            
            # 过滤掉完全为空的行
            df = df.dropna(how='all')
            
            # 输出诊断信息
            print(f"Excel读取：共有 {len(df)} 行数据")
            empty_sku = df[df['SKU'].isna()]
            empty_asin = df[df['ASIN'].isna()]
            print(f"SKU为空的行数: {len(empty_sku)}")
            print(f"ASIN为空的行数: {len(empty_asin)}")
            
            # 过滤掉SKU或ASIN为空的行，只保留完整数据
            df_valid = df.dropna(subset=['SKU', 'ASIN'])
            print(f"过滤后的有效数据行数: {len(df_valid)}")
            
            if len(df_valid) == 0:
                self.error_message = 'Excel文件中没有包含完整SKU和ASIN的有效数据行'
                raise Exception(self.error_message)
            
            self.sku_to_asin = df_valid.set_index('SKU')['ASIN'].to_dict()
            
            # 如果存在Color列，创建颜色到ASIN列表的映射
            if 'Color' in df_valid.columns:
                self.color_to_asin = {}
                for _, row in df_valid.iterrows():
                    if pd.notna(row['Color']):
                        color = str(row['Color']).strip()
                        asin = str(row['ASIN']).strip()

                        # 为每种颜色创建ASIN列表
                        color_variants = [
                            color,
                            color.replace(' ', ''),
                            color.lower(),
                            color.lower().replace(' ', '')
                        ]

                        for color_variant in color_variants:
                            if color_variant not in self.color_to_asin:
                                self.color_to_asin[color_variant] = []
                            if asin not in self.color_to_asin[color_variant]:
                                self.color_to_asin[color_variant].append(asin)
            
            return True
            
        except Exception as e:
            self.error_message = str(e)
            print(f"Excel读取错误: {self.error_message}")
            return False
    
    def process_images(self, process_main=False, process_scene=False, process_swatch=False, scene_generic=False):
        """
        处理图片重命名
        
        Args:
            process_main: 是否处理主图
            process_scene: 是否处理场景图
            process_swatch: 是否处理色块图
            scene_generic: 是否使用通用类型场景图处理
            
        Returns:
            int: 成功处理的文件数量
        """
        total_processed = 0
        
        if process_main:
            total_processed += self._process_main_images()
        if process_scene:
            total_processed += self._process_scene_images(scene_generic)
        if process_swatch:
            total_processed += self._process_swatch_images()
            
        return total_processed
    
    def _process_main_images(self):
        """处理主图（白底图）"""
        print("开始处理主图...")
        # 初始化成功处理计数器
        success_count = 0

        # 遍历所有上传的文件
        for root, dirs, files in os.walk(self.image_path):
            # 检查当前目录是否为"白底图"目录
            if os.path.basename(root) == "白底图":
                # 从路径中提取SKU
                parts = root.split(os.sep)
                sku_index = parts.index("白底图") - 1
                if sku_index >= 0:
                    # 获取SKU和对应的ASIN
                    sku = parts[sku_index]
                    asin = self.sku_to_asin.get(sku)

                    if asin:
                        # 打印处理信息
                        print(f"处理SKU: {sku}, ASIN: {asin}")
                        # 处理所有图片文件
                        for file in files:
                            if file.lower().endswith(('.png', '.jpg', '.jpeg', '.gif', '.bmp')):
                                file_path = os.path.join(root, file)
                                if self._rename_file(root, file, asin, "MAIN"):
                                    success_count += 1
                    else:
                        print(f"未找到SKU对应的ASIN: {sku}")

        print(f"主图处理完成：成功处理 {success_count} 个文件")
        return success_count
        
    def _process_scene_images(self, scene_generic=False):
        """处理场景图"""
        if scene_generic:
            return self._process_scene_images_generic()
            
        print("开始处理场景图...")
        success_count = 0
        
        # 遍历所有上传的文件
        for root, dirs, files in os.walk(self.image_path):
            # 检查当前目录是否为"场景图"或"卖点图"目录
            if os.path.basename(root) in ["场景图", "卖点图"]:
                # 从路径中提取SKU
                parts = root.split(os.sep)
                dir_name = os.path.basename(root)
                sku_index = parts.index(dir_name) - 1
                if sku_index >= 0:
                    # 获取SKU和对应的ASIN
                    sku = parts[sku_index]
                    asin = self.sku_to_asin.get(sku)

                    if asin:
                        # 打印处理信息
                        print(f"处理SKU: {sku}, ASIN: {asin}")
                        
                        # 处理所有图片文件，按文件名排序确保编号顺序
                        sorted_files = sorted([f for f in files if f.lower().endswith(('.png', '.jpg', '.jpeg', '.gif', '.bmp'))])

                        for idx, file in enumerate(sorted_files, 1):
                            # 尝试从文件名中提取PT编号
                            pt_match = re.search(r'PT(\d+)', file.upper())
                            if pt_match:
                                pt_num = int(pt_match.group(1))
                                variant_code = f"PT{pt_num:02d}"
                            else:
                                # 如果文件名中没有PT编号，使用序号作为编号
                                variant_code = f"PT{idx:02d}"
                                
                                if self._rename_file(root, file, asin, variant_code):
                                    success_count += 1
                    else:
                        print(f"未找到SKU对应的ASIN: {sku}")
        
        print(f"场景图处理完成：成功处理 {success_count} 个文件")
        return success_count

    def _process_scene_images_generic(self):
        """处理通用类型场景图"""
        print("\n===== 开始处理通用类型场景图 =====")
        success_count = 0
        
        # 步骤1: 读取Excel数据，获取ASIN和Width的对应关系
        try:
            df = pd.read_excel(self.excel_path)
            print(f"Excel读取成功，共有 {len(df)} 行数据")
            
            if 'Width' not in df.columns or 'ASIN' not in df.columns:
                print("错误: Excel文件中缺少必要的列 'Width' 或 'ASIN'")
                return 0
                
            # 过滤有效数据
            df_valid = df.dropna(subset=['Width', 'ASIN'])
            print(f"有效数据行数(Width和ASIN都不为空): {len(df_valid)}")
            
            if len(df_valid) == 0:
                print("错误: 没有有效的Width和ASIN数据")
                return 0
                
            # 步骤2: 将ASIN按宽度范围分类
            width_ranges = {
                "2~5": (2, 5),
                "8~12": (8, 12),
                "16~24": (16, 24)
            }
            
            # 初始化宽度范围到ASIN的映射
            width_to_asin = {range_name: [] for range_name in width_ranges.keys()}
            
            # 分类ASIN
            for _, row in df_valid.iterrows():
                width_str = str(row['Width']).strip()
                asin = str(row['ASIN']).strip()
                
                # 提取数值部分 - 改进解析逻辑
                width_value = width_str
                if '分' in width_value:
                    width_value = width_value.replace('分', '')
                
                # 移除所有非数字和小数点字符
                width_value = ''.join(c for c in width_value if c.isdigit() or c == '.')
                
                try:
                    width = float(width_value)
                    # 查找匹配的宽度范围
                    matched = False
                    for range_name, (min_width, max_width) in width_ranges.items():
                        if min_width <= width <= max_width:
                            width_to_asin[range_name].append(asin)
                            print(f"ASIN: {asin}, 宽度: {width_str} ({width}), 分类到: {range_name}")
                            matched = True
                            break
                            
                    if not matched:
                        print(f"警告: ASIN {asin} 的宽度值 {width} 不在任何预定义范围内")
                except ValueError:
                    print(f"警告: 无法解析宽度值 '{width_str}' 为数值")
            
            # 输出分类结果
            for range_name, asins in width_to_asin.items():
                print(f"\n{range_name} 范围: {len(asins)} 个ASIN")
                if asins:
                    print(f"  示例: {asins[:5]}")
            
            # 检查是否有ASIN被分类
            if all(len(asins) == 0 for asins in width_to_asin.values()):
                print("错误: 没有ASIN被分类到任何宽度范围！")
                return 0
                
        except Exception as e:
            print(f"处理Excel数据时出错: {str(e)}")
            import traceback
            traceback.print_exc()
            return 0
            
        # 步骤3: 创建输出目录结构
        base_dir = os.path.dirname(self.image_path)
        print(f"\n使用基础目录: {base_dir}")
        
        # 确保宽度范围目录存在
        output_folders = {}
        for range_name, asins in width_to_asin.items():
            if not asins:  # 跳过没有ASIN的宽度范围
                continue
                
            output_folder = os.path.join(base_dir, range_name)
            os.makedirs(output_folder, exist_ok=True)
            output_folders[range_name] = output_folder
            print(f"创建输出目录: {output_folder}")
        
        # 步骤4: 查找场景图目录和PT文件
        scene_dirs = []
        for root, dirs, files in os.walk(self.image_path):
            if os.path.basename(root) == "卖点图" or os.path.basename(root) == "场景图":
                scene_dirs.append(root)
        
        print(f"\n找到场景图目录: {len(scene_dirs)} 个")
        
        # 步骤5: 处理每个场景图目录
        for scene_dir in scene_dirs:
            print(f"\n处理目录: {scene_dir}")
            
            # 获取SKU (从目录路径提取)
            parts = scene_dir.split(os.sep)
            dir_name = os.path.basename(scene_dir)
            sku_index = parts.index(dir_name) - 1
            
            if sku_index < 0:
                print(f"无法从路径中提取SKU: {scene_dir}")
                continue
                
            sku = parts[sku_index]
            asin = self.sku_to_asin.get(sku)
            
            if not asin:
                print(f"未找到SKU对应的ASIN: {sku}")
                continue
                
            # 确定宽度范围
            width_range = None
            for range_name, asins in width_to_asin.items():
                if asin in asins:
                    width_range = range_name
                    break
                    
            if not width_range:
                print(f"未找到ASIN对应的宽度范围: {asin}")
                continue
                
            output_folder = output_folders[width_range]
            print(f"SKU: {sku}, ASIN: {asin}, 宽度范围: {width_range}")
            
            # 处理目录中的所有图片，按文件名排序确保编号顺序
            image_files = sorted([f for f in os.listdir(scene_dir) if f.lower().endswith(('.png', '.jpg', '.jpeg', '.gif', '.bmp'))])

            for idx, file in enumerate(image_files, 1):
                # 提取PT编号
                pt_match = re.search(r'PT(\d+)', file.upper())
                if pt_match:
                    pt_num = int(pt_match.group(1))
                    variant_code = f"PT{pt_num:02d}"
                else:
                    # 如果文件名中没有PT编号，使用序号作为编号
                    variant_code = f"PT{idx:02d}"
                        
                    # 创建新文件名
                    new_filename = f"{asin}_{variant_code}.jpg"
                    src_path = os.path.join(scene_dir, file)
                    dst_path = os.path.join(output_folder, new_filename)
                    
                    # 复制并重命名文件
                    try:
                        shutil.copy2(src_path, dst_path)
                        print(f"  复制文件: {file} -> {new_filename}")
                        self.processed_files.append(dst_path)
                        success_count += 1
                    except Exception as e:
                        print(f"  复制文件失败: {file} -> {new_filename}: {e}")
        
        print(f"\n通用类型场景图处理完成: 成功处理 {success_count} 个文件")
        return success_count
    
    def _process_swatch_images(self):
        """处理色块图"""
        print("\n开始处理色块图...")
        success_count = 0

        # 检查是否有颜色到ASIN的映射
        if not self.color_to_asin:
            print("错误: 没有颜色到ASIN的映射数据，请确保Excel文件包含Color列")
            return 0

        # 调试信息：显示颜色映射
        print(f"🔍 [调试] 颜色到ASIN映射数量: {len(self.color_to_asin)}")
        # 显示前3个颜色映射的示例
        sample_mappings = {}
        for i, (color, asin_list) in enumerate(self.color_to_asin.items()):
            if i >= 3:
                break
            sample_mappings[color] = asin_list
        print(f"🔍 [调试] 颜色映射示例: {sample_mappings}")
        print(f"🔍 [调试] 图片目录路径: {self.image_path}")
            
        # 遍历所有上传的文件
        swatch_folder_found = False
        for root, dirs, files in os.walk(self.image_path):
            print(f"🔍 [调试] 检查目录: {root}, 目录名: {os.path.basename(root)}")
            # 检查当前目录是否包含"色块"关键词
            if "色块" in os.path.basename(root):
                swatch_folder_found = True
                print(f"✅ 找到色块目录: {root}")
                print(f"🔍 [调试] 色块目录中的文件: {files}")

                # 处理所有图片文件
                image_files = [f for f in files if f.lower().endswith(('.png', '.jpg', '.jpeg', '.gif', '.bmp'))]
                print(f"🔍 [调试] 色块目录中的图片文件: {image_files}")

                for file in image_files:
                        # 从文件名中提取颜色信息
                        file_base = os.path.splitext(file)[0]
                        color = None
                        
                        # 尝试不同的文件名格式
                        if file_base.lower().startswith('swch_'):
                            # 格式: swch_color
                            color = file_base[5:]
                        else:
                            # 直接使用文件名作为颜色
                            color = file_base
                            
                        # 尝试查找颜色对应的ASIN列表
                        matched_asins = []
                        for color_variant, asin_list in self.color_to_asin.items():
                            if color.lower() == color_variant.lower() or color.lower().replace(' ', '') == color_variant.lower().replace(' ', ''):
                                matched_asins = asin_list
                                break

                        if matched_asins:
                            print(f"  找到颜色匹配: {color} -> ASINs: {matched_asins}")
                            # 为每个ASIN创建一个文件
                            for asin in matched_asins:
                                if self._rename_file(root, file, asin, "SWCH"):
                                    success_count += 1
                        else:
                            print(f"  未找到颜色对应的ASIN: {color}")

        # 如果没有找到色块文件夹，给出详细提示
        if not swatch_folder_found:
            print("⚠️ 未找到包含【色块】字样的文件夹")
            print("💡 请确保图片文件夹中包含名称含有【色块】字样的子目录")
            print("📁 支持的文件夹名称示例：色块、色块-317、swatch色块、色块图片 等")

        print(f"色块图处理完成: 成功处理 {success_count} 个文件")
        return success_count
    
    def _rename_file(self, folder, file, asin, variant_code):
        """
        重命名单个文件
        
        Args:
            folder: 文件所在目录
            file: 文件名
            asin: ASIN编码
            variant_code: 变体代码 (MAIN, PT01, SWCH等)
            
        Returns:
            bool: 是否成功重命名
        """
        try:
            # 创建新文件名
            new_filename = f"{asin}_{variant_code}.jpg"
            src_path = os.path.join(folder, file)
            dst_path = os.path.join(folder, new_filename)
            
            # 复制并重命名文件
            shutil.copy2(src_path, dst_path)
            print(f"  重命名文件: {file} -> {new_filename}")
            self.processed_files.append(dst_path)
            return True
        except Exception as e:
            print(f"  重命名文件失败: {file}: {e}")
            return False
    
    def create_zip(self, folder_name=None):
        """
        将处理后的文件打包为ZIP
        
        Args:
            folder_name: ZIP文件名前缀
            
        Returns:
            BytesIO: 内存中的ZIP文件
        """
        if not self.processed_files:
            raise Exception("没有处理后的文件可打包")
            
        # 创建内存文件对象
        memory_file = BytesIO()
        
        # 创建ZIP文件
        with zipfile.ZipFile(memory_file, 'w', zipfile.ZIP_DEFLATED) as zipf:
            # 添加所有处理后的文件
            for file_path in self.processed_files:
                # 获取相对路径
                arcname = os.path.basename(file_path)
                zipf.write(file_path, arcname=arcname)
                
        # 将指针移到文件开头
        memory_file.seek(0)
        return memory_file

def analyze_color_matching(excel_file, image_folder):
    """
    分析颜色匹配问题
    
    Args:
        excel_file: Excel文件路径
        image_folder: 图片目录路径
        
    Returns:
        Dict: 分析结果
    """
    # 读取Excel数据
    try:
        df = pd.read_excel(excel_file)
        if 'Color' not in df.columns:
            return {
                "status": "error",
                "message": "❌ Excel文件中缺少Color列，无法进行颜色匹配测试"
            }
            
        excel_colors = df['Color'].dropna().unique()
        excel_color_list = [str(color) for color in excel_colors]
        
        # 查找色块图片
        swatch_folder_found = False
        image_files_found = []
        
        for root, dirs, files in os.walk(image_folder):
            if "色块" in os.path.basename(root):
                swatch_folder_found = True
                image_files = [f for f in files if f.lower().endswith(('.png', '.jpg', '.jpeg', '.gif', '.bmp'))]
                
                for file in image_files:
                    file_lower = file.lower()
                    base_name = os.path.splitext(file_lower)[0]
                    
                    if base_name.startswith('swch_'):
                        color_name = base_name.replace('swch_', '')
                    else:
                        color_name = base_name
                    
                    image_files_found.append({
                        'filename': file,
                        'color_name': color_name
                    })
        
        # 如果没有找到色块文件夹
        if not swatch_folder_found:
            return {
                "status": "warning",
                "message": "⚠️ 未找到包含【色块】字样的文件夹\n\n💡 请确保您的图片文件夹中包含名称含有【色块】字样的子目录\n📁 支持的文件夹名称示例：色块、色块-317、swatch色块、色块图片 等"
            }
        
        # 如果色块文件夹为空
        if not image_files_found:
            return {
                "status": "warning",
                "message": "⚠️ 色块文件夹中未找到任何图片文件\n\n💡 请确保色块文件夹中包含图片文件（支持格式：jpg、png、jpeg、gif、bmp）"
            }
        
        # 进行颜色匹配分析
        matched_count = 0
        unmatched_colors = []
        matched_details = []
        
        for excel_color in excel_color_list:
            # 创建颜色变体列表用于智能匹配
            color_variations = [
                excel_color,
                excel_color.replace(' ', ''),
                excel_color.lower(),
                excel_color.lower().replace(' ', ''),
                excel_color.replace('-', ''),
                excel_color.replace('_', ''),
            ]
            
            # 查找匹配的图片文件
            matched = False
            matched_file = None
            
            for img_info in image_files_found:
                img_color = img_info['color_name']
                img_variations = [
                    img_color,
                    img_color.replace(' ', ''),
                    img_color.lower(),
                    img_color.lower().replace(' ', ''),
                    img_color.replace('-', ''),
                    img_color.replace('_', ''),
                ]
                
                # 检查是否有任何变体匹配
                if any(excel_var in img_variations for excel_var in color_variations):
                    matched = True
                    matched_file = img_info['filename']
                    break
            
            if matched:
                matched_count += 1
                matched_details.append(f"✅ {excel_color} ➜ {matched_file}")
            else:
                unmatched_colors.append(excel_color)
        
        # 生成用户友好的总结报告
        total_colors = len(excel_color_list)
        match_rate = (matched_count / total_colors * 100) if total_colors > 0 else 0
        
        # 构建总结信息
        summary_lines = [
            f"📊 颜色匹配测试结果",
            f"",
            f"📈 总体情况:",
            f"   • Excel中的颜色数量: {total_colors}",
            f"   • 色块图片数量: {len(image_files_found)}",
            f"   • 成功匹配数量: {matched_count}",
            f"   • 匹配成功率: {match_rate:.1f}%",
            f""
        ]
        
        if match_rate >= 90:
            summary_lines.extend([
                f"🎉 匹配率优秀！您的文件准备得很好",
                f"✅ 建议：可以直接进行重命名处理"
            ])
        elif match_rate >= 70:
            summary_lines.extend([
                f"👍 匹配率良好，但还有改进空间",
                f"💡 建议：检查下方未匹配的颜色"
            ])
        else:
            summary_lines.extend([
                f"⚠️ 匹配率较低，需要检查文件命名",
                f"🔧 建议：仔细检查颜色名称的拼写和格式"
            ])
        
        # 添加详细匹配信息
        if matched_details:
            summary_lines.extend([
                f"",
                f"✅ 成功匹配的颜色:"
            ])
            summary_lines.extend(matched_details[:10])  # 最多显示10个
            if len(matched_details) > 10:
                summary_lines.append(f"   ... 还有 {len(matched_details) - 10} 个匹配项")
        
        # 添加未匹配信息
        if unmatched_colors:
            summary_lines.extend([
                f"",
                f"❌ 未匹配的颜色（需要检查）:"
            ])
            for color in unmatched_colors[:10]:  # 最多显示10个
                summary_lines.append(f"   • {color}")
            if len(unmatched_colors) > 10:
                summary_lines.append(f"   ... 还有 {len(unmatched_colors) - 10} 个未匹配")
                
            summary_lines.extend([
                f"",
                f"💡 解决建议:",
                f"   1. 检查颜色名称的拼写是否一致",
                f"   2. 确保图片文件命名格式正确",
                f"   3. 颜色名称支持大小写、空格、下划线的智能匹配"
            ])
        
        return {
            "status": "success",
            "message": "\n".join(summary_lines),
            "summary": {
                "total_excel_colors": total_colors,
                "total_image_files": len(image_files_found),
                "matched_count": matched_count,
                "match_rate": round(match_rate, 1),
                "unmatched_count": len(unmatched_colors)
            }
        }
        
    except Exception as e:
        import traceback
        traceback.print_exc()
        return {
            "status": "error",
            "message": f"❌ 测试过程中发生错误: {str(e)}"
        }

def process_rename_task(task_id, excel_path, image_path, options):
    """
    处理重命名任务
    
    Args:
        task_id: 任务ID
        excel_path: Excel文件路径
        image_path: 图片目录路径
        options: 处理选项
        
    Returns:
        Tuple[bool, str, BytesIO]: (是否成功, 消息, ZIP文件内存对象)
    """
    try:
        # 创建重命名处理器
        renamer = ImageRenamer(excel_path, image_path)
        
        # 加载Excel数据
        if not renamer.load_excel_data():
            return False, renamer.error_message, None
            
        # 处理图片
        total_processed = renamer.process_images(
            process_main=options.get('process_main', False),
            process_scene=options.get('process_scene', False),
            process_swatch=options.get('process_swatch', False),
            scene_generic=options.get('scene_generic', False)
        )
        
        if total_processed == 0:
            return False, "没有处理任何文件，请检查选项和文件目录结构", None
        
        # 创建ZIP文件
        zip_memory_file = renamer.create_zip(f"renamed_images_{task_id}")
        
        return True, f"成功处理 {total_processed} 个文件", zip_memory_file
        
    except Exception as e:
        import traceback
        traceback.print_exc()
        return False, f"处理失败: {str(e)}", None 